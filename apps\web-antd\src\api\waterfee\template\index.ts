import type {
  TemplateCategoryOption,
  TemplateTypeOption,
  WaterfeeTemplate,
  WaterfeeTemplateForm,
  WaterfeeTemplateQuery,
} from './model';

import type { ID, IDS, PageQuery, PageResult } from '#/api/common';

import { requestClient } from '#/api/request';

enum Api {
  categories = '/waterfee/template/categories',
  disable = '/waterfee/template/disable',
  enable = '/waterfee/template/enable',
  enabled = '/waterfee/template/enabled',
  root = '/waterfee/template',
  templateList = '/waterfee/template/list',
  types = '/waterfee/template/types',
}

/**
 * 查询模板管理列表
 * @param params 查询参数
 * @returns 分页结果
 */
export function templateList(params?: PageQuery & WaterfeeTemplateQuery) {
  return requestClient.get<PageResult<WaterfeeTemplate>>(Api.templateList, {
    params,
  });
}

/**
 * 获取模板管理详细信息
 * @param templateId 模板ID
 * @returns 模板详情
 */
export function templateInfo(templateId: ID) {
  return requestClient.get<WaterfeeTemplate>(`${Api.root}/${templateId}`);
}

/**
 * 新增模板管理
 * @param data 模板数据
 */
export function templateAdd(data: WaterfeeTemplateForm) {
  return requestClient.postWithMsg<void>(Api.root, data);
}

/**
 * 修改模板管理
 * @param data 模板数据
 */
export function templateUpdate(data: WaterfeeTemplateForm) {
  return requestClient.putWithMsg<void>(Api.root, data);
}

/**
 * 删除模板管理
 * @param templateIds 模板ID数组
 */
export function templateRemove(templateIds: IDS) {
  return requestClient.deleteWithMsg<void>(`${Api.root}/${templateIds}`);
}

/**
 * 启用模板
 * @param templateId 模板ID
 */
export function templateEnable(templateId: ID) {
  return requestClient.putWithMsg<void>(`${Api.enable}/${templateId}`);
}

/**
 * 禁用模板
 * @param templateId 模板ID
 */
export function templateDisable(templateId: ID) {
  return requestClient.putWithMsg<void>(`${Api.disable}/${templateId}`);
}

/**
 * 获取启用的模板
 * @param templateType 模板类型
 * @param templateCategory 模板分类（可选）
 */
export function getEnabledTemplate(
  templateType: string,
  templateCategory?: string,
) {
  const params: Record<string, string> = { templateType };
  if (templateCategory) {
    params.templateCategory = templateCategory;
  }
  return requestClient.get<WaterfeeTemplate>(Api.enabled, { params });
}

/**
 * 获取模板类型列表
 */
export function getTemplateTypes() {
  return requestClient.get<TemplateTypeOption[]>(Api.types);
}

/**
 * 获取模板分类列表
 */
export function getTemplateCategories() {
  return requestClient.get<TemplateCategoryOption[]>(Api.categories);
}
