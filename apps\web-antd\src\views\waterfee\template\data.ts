import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { h } from 'vue';

import { getPopupContainer } from '@vben/utils';

import {
  getTemplateCategories,
  getTemplateTypes,
} from '#/api/waterfee/template';

/**
 * 查询表单配置
 */
export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'templateName',
    label: '模板名称',
    componentProps: {
      placeholder: '请输入模板名称',
    },
  },
  {
    component: 'ApiSelect',
    fieldName: 'templateType',
    label: '模板类型',
    componentProps: {
      api: getTemplateTypes,
      resultField: 'data',
      labelField: 'name',
      valueField: 'code',
    },
  },
  {
    component: 'ApiSelect',
    fieldName: 'templateCategory',
    label: '模板分类',
    componentProps: {
      api: getTemplateCategories,
      resultField: 'data',
      labelField: 'name',
      valueField: 'code',
    },
  },
  {
    component: 'Select',
    fieldName: 'isEnabled',
    label: '启用状态',
    componentProps: {
      placeholder: '请选择启用状态',
      getPopupContainer,
      options: [
        { label: '启用', value: '1' },
        { label: '禁用', value: '0' },
      ],
    },
  },
  {
    component: 'Input',
    fieldName: 'uploadBy',
    label: '上传者',
    componentProps: {
      placeholder: '请输入上传者',
    },
  },
  {
    component: 'RangePicker',
    fieldName: 'uploadTime',
    label: '上传时间',
    componentProps: {
      placeholder: ['开始时间', '结束时间'],
    },
  },
];

/**
 * 表格列配置
 */
export const columns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 60 },
  {
    title: '模板名称',
    field: 'templateName',
    minWidth: 150,
  },
  {
    title: '模板类型',
    field: 'templateType',
    width: 120,
    slots: {
      default: ({ row }) => {
        const typeMap: Record<string, string> = {
          WORD: 'Word文档',
          EXCEL: 'Excel文档',
          PDF: 'PDF文档',
        };
        return typeMap[row.templateType] || row.templateType;
      },
    },
  },
  {
    title: '模板分类',
    field: 'templateCategory',
    width: 120,
    slots: {
      default: ({ row }) => {
        const categoryMap: Record<string, string> = {
          BILL: '账单模板',
          REPORT: '报表模板',
          NOTICE: '通知模板',
        };
        return categoryMap[row.templateCategory] || row.templateCategory;
      },
    },
  },
  {
    title: '文件名称',
    field: 'fileName',
    minWidth: 150,
  },
  {
    title: '启用状态',
    field: 'isEnabled',
    width: 100,
    slots: {
      default: ({ row }) => {
        return h(
          'span',
          {
            class: row.isEnabled === '1' ? 'text-green-600' : 'text-red-600',
          },
          row.isEnabled === '1' ? '启用' : '禁用',
        );
      },
    },
  },
  {
    title: '版本号',
    field: 'version',
    width: 100,
  },
  {
    title: '上传者',
    field: 'uploadBy',
    width: 120,
  },
  {
    title: '上传时间',
    field: 'uploadTime',
    width: 160,
  },
  {
    title: '启用者',
    field: 'enabledBy',
    width: 120,
  },
  {
    title: '启用时间',
    field: 'enabledTime',
    width: 160,
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 200,
  },
];

/**
 * 模态框表单配置
 */
export const modalSchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
    fieldName: 'templateId',
    label: '模板ID',
  },
  {
    component: 'Input',
    fieldName: 'templateName',
    label: '模板名称',
    rules: 'required',
    componentProps: {
      placeholder: '请输入模板名称',
    },
  },
  {
    component: 'ApiSelect',
    fieldName: 'templateType',
    label: '模板类型',
    rules: 'required',
    componentProps: {
      api: getTemplateTypes,
      resultField: 'data',
      labelField: 'name',
      valueField: 'code',
    },
    formItemClass: 'col-span-1',
  },
  {
    component: 'ApiSelect',
    fieldName: 'templateCategory',
    label: '模板分类',
    rules: 'required',
    componentProps: {
      api: getTemplateCategories,
      resultField: 'data',
      labelField: 'name',
      valueField: 'code',
    },
    formItemClass: 'col-span-1',
  },
  {
    component: 'FileUpload',
    fieldName: 'fileUrl',
    label: '模板文件',
    rules: 'required',
    componentProps: {
      accept: ['doc', 'docx', 'xls', 'xlsx', 'pdf'],
      maxNumber: 1,
      maxSize: 10,
      resultField: 'url',
    },
  },
  {
    component: 'RadioGroup',
    fieldName: 'isEnabled',
    label: '启用状态',
    rules: 'required',
    defaultValue: '1',
    componentProps: {
      buttonStyle: 'solid',
      optionType: 'button',
      options: [
        { label: '启用', value: '1' },
        { label: '禁用', value: '0' },
      ],
    },
    formItemClass: 'col-span-1',
  },
  {
    component: 'Input',
    fieldName: 'version',
    label: '版本号',
    componentProps: {
      placeholder: '请输入版本号',
    },
    formItemClass: 'col-span-1',
  },
  {
    component: 'Textarea',
    fieldName: 'templateDescription',
    label: '模板描述',
    componentProps: {
      placeholder: '请输入模板描述',
      rows: 3,
    },
  },
  {
    component: 'Textarea',
    fieldName: 'remark',
    label: '备注',
    componentProps: {
      placeholder: '请输入备注',
      rows: 2,
    },
  },
];

/**
 * 初始化下拉选项数据
 */
// export async function initSelectOptions() {
//   try {
//     const [typesRes, categoriesRes] = await Promise.all([
//       getTemplateTypes(),
//       getTemplateCategories(),
//     ]);

//     // 更新查询表单的选项
//     const querySchemaData = querySchema();
//     const typeField = querySchemaData.find(
//       (item) => item.fieldName === 'templateType',
//     );
//     const categoryField = querySchemaData.find(
//       (item) => item.fieldName === 'templateCategory',
//     );

//     if (typeField?.componentProps) {
//       typeField.componentProps.options = typesRes.map((item) => ({
//         label: item.name,
//         value: item.code,
//       }));
//     }

//     if (categoryField?.componentProps) {
//       categoryField.componentProps.options = categoriesRes.map((item) => ({
//         label: item.name,
//         value: item.code,
//       }));
//     }

//     // 更新模态框表单的选项
//     const modalSchemaData = modalSchema();
//     const modalTypeField = modalSchemaData.find(
//       (item) => item.fieldName === 'templateType',
//     );
//     const modalCategoryField = modalSchemaData.find(
//       (item) => item.fieldName === 'templateCategory',
//     );

//     if (modalTypeField?.componentProps) {
//       modalTypeField.componentProps.options = typesRes.map((item) => ({
//         label: item.name,
//         value: item.code,
//       }));
//     }

//     if (modalCategoryField?.componentProps) {
//       modalCategoryField.componentProps.options = categoriesRes.map((item) => ({
//         label: item.name,
//         value: item.code,
//       }));
//     }

//     return { types: typesRes, categories: categoriesRes };
//   } catch (error) {
//     console.error('初始化下拉选项失败:', error);
//     return { types: [], categories: [] };
//   }
// }
