/**
 * 模板管理相关类型定义
 */

/**
 * 模板管理查询参数
 */
export interface WaterfeeTemplateQuery {
  /** 模板名称 */
  templateName?: string;
  /** 模板类型（WORD-Word文档 EXCEL-Excel文档 PDF-PDF文档） */
  templateType?: string;
  /** 模板分类（BILL-账单模板 REPORT-报表模板 NOTICE-通知模板） */
  templateCategory?: string;
  /** 是否启用（0-禁用 1-启用） */
  isEnabled?: string;
  /** 上传者 */
  uploadBy?: string;
  /** 上传时间范围 */
  uploadTime?: [string, string];
}

/**
 * 模板管理实体
 */
export interface WaterfeeTemplate {
  /** 模板ID */
  templateId: number;
  /** 租户编号 */
  tenantId?: string;
  /** 模板名称 */
  templateName: string;
  /** 模板类型（WORD-Word文档 EXCEL-Excel文档 PDF-PDF文档） */
  templateType: string;
  /** 模板分类（BILL-账单模板 REPORT-报表模板 NOTICE-通知模板） */
  templateCategory: string;
  /** 文件名称 */
  fileName: string;
  /** 文件访问URL */
  fileUrl: string;
  /** 是否启用（0-禁用 1-启用） */
  isEnabled: string;
  /** 模板描述 */
  templateDescription?: string;
  /** 上传者 */
  uploadBy?: string;
  /** 上传时间 */
  uploadTime?: string;
  /** 启用者 */
  enabledBy?: string;
  /** 启用时间 */
  enabledTime?: string;
  /** 版本号 */
  version?: string;
  /** 备注 */
  remark?: string;
  /** 删除标志（0代表存在 2代表删除） */
  delFlag?: string;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
}

/**
 * 模板管理表单数据
 */
export interface WaterfeeTemplateForm {
  /** 模板ID */
  templateId?: number;
  /** 模板名称 */
  templateName: string;
  /** 模板类型 */
  templateType: string;
  /** 模板分类 */
  templateCategory: string;
  /** 文件名称 */
  fileName?: string;
  /** 文件访问URL */
  fileUrl: string;
  /** 是否启用 */
  isEnabled: string;
  /** 模板描述 */
  templateDescription?: string;
  /** 版本号 */
  version?: string;
  /** 备注 */
  remark?: string;
}

/**
 * 模板类型选项
 */
export interface TemplateTypeOption {
  label: string;
  value: string;
}

/**
 * 模板分类选项
 */
export interface TemplateCategoryOption {
  label: string;
  value: string;
}
