<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';

import type { VxeGridProps } from '#/adapter/vxe-table';
import type { WaterfeeTemplate } from '#/api/waterfee/template/model';

import { onMounted } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { getVxePopupContainer } from '@vben/utils';

import { Modal, Popconfirm, Space } from 'ant-design-vue';

import { useVbenVxeGrid, vxeCheckboxChecked } from '#/adapter/vxe-table';
import {
  templateDisable,
  templateEnable,
  templateList,
  templateRemove,
} from '#/api/waterfee/template';

import { columns, querySchema } from './data';
import templateModal from './template-modal.vue';

const formOptions: VbenFormProps = {
  commonConfig: {
    labelWidth: 80,
    componentProps: {
      allowClear: true,
    },
  },
  schema: querySchema(),
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
};

const gridOptions: VxeGridProps = {
  checkboxConfig: {
    // 高亮
    highlight: true,
    // 翻页时保留选中状态
    reserve: true,
    // 点击行选中
    // trigger: 'row',
  },
  columns,
  height: 'auto',
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues = {}) => {
        return await templateList({
          pageNum: page.currentPage,
          pageSize: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  rowConfig: {
    keyField: 'templateId',
  },
  id: 'waterfee-template-index',
};

const [BasicTable, tableApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

const [TemplateModal, modalApi] = useVbenModal({
  connectedComponent: templateModal,
});

// 初始化下拉选项
onMounted(async () => {
  // await initSelectOptions();
});

function handleAdd() {
  modalApi.setData({});
  modalApi.open();
}

async function handleEdit(record: WaterfeeTemplate) {
  modalApi.setData({ id: record.templateId });
  modalApi.open();
}

async function handleDelete(row: WaterfeeTemplate) {
  await templateRemove([row.templateId]);
  await tableApi.query();
}

function handleMultiDelete() {
  const rows = tableApi.grid.getCheckboxRecords();
  const ids = rows.map((row: WaterfeeTemplate) => row.templateId);
  Modal.confirm({
    title: '提示',
    okType: 'danger',
    content: `确认删除选中的${ids.length}条记录吗？`,
    onOk: async () => {
      await templateRemove(ids);
      await tableApi.query();
    },
  });
}

async function handleEnable(row: WaterfeeTemplate) {
  await templateEnable(row.templateId);
  await tableApi.query();
}

async function handleDisable(row: WaterfeeTemplate) {
  await templateDisable(row.templateId);
  await tableApi.query();
}

function handleMultiEnable() {
  const rows = tableApi.grid.getCheckboxRecords();
  const ids = rows.map((row: WaterfeeTemplate) => row.templateId);
  Modal.confirm({
    title: '提示',
    content: `确认启用选中的${ids.length}条模板吗？`,
    onOk: async () => {
      for (const id of ids) {
        await templateEnable(id);
      }
      await tableApi.query();
    },
  });
}

function handleMultiDisable() {
  const rows = tableApi.grid.getCheckboxRecords();
  const ids = rows.map((row: WaterfeeTemplate) => row.templateId);
  Modal.confirm({
    title: '提示',
    content: `确认禁用选中的${ids.length}条模板吗？`,
    onOk: async () => {
      for (const id of ids) {
        await templateDisable(id);
      }
      await tableApi.query();
    },
  });
}
</script>

<template>
  <Page :auto-content-height="true">
    <BasicTable table-title="模板管理列表">
      <template #toolbar-tools>
        <Space>
          <a-button
            :disabled="!vxeCheckboxChecked(tableApi)"
            danger
            type="primary"
            v-access:code="['waterfee:template:remove']"
            @click="handleMultiDelete"
          >
            {{ $t('pages.common.delete') }}
          </a-button>
          <a-button
            :disabled="!vxeCheckboxChecked(tableApi)"
            type="default"
            v-access:code="['waterfee:template:enable']"
            @click="handleMultiEnable"
          >
            批量启用
          </a-button>
          <a-button
            :disabled="!vxeCheckboxChecked(tableApi)"
            type="default"
            v-access:code="['waterfee:template:disable']"
            @click="handleMultiDisable"
          >
            批量禁用
          </a-button>
          <a-button
            type="primary"
            v-access:code="['waterfee:template:add']"
            @click="handleAdd"
          >
            {{ $t('pages.common.add') }}
          </a-button>
        </Space>
      </template>
      <template #action="{ row }">
        <Space>
          <ghost-button
            v-access:code="['waterfee:template:edit']"
            @click="handleEdit(row)"
          >
            {{ $t('pages.common.edit') }}
          </ghost-button>
          <ghost-button
            v-if="row.isEnabled === '0'"
            v-access:code="['waterfee:template:enable']"
            type="primary"
            @click="handleEnable(row)"
          >
            启用
          </ghost-button>
          <ghost-button
            v-if="row.isEnabled === '1'"
            v-access:code="['waterfee:template:disable']"
            @click="handleDisable(row)"
          >
            禁用
          </ghost-button>
          <Popconfirm
            :get-popup-container="getVxePopupContainer"
            placement="left"
            title="确认删除？"
            @confirm="handleDelete(row)"
          >
            <ghost-button
              danger
              v-access:code="['waterfee:template:remove']"
              @click.stop=""
            >
              {{ $t('pages.common.delete') }}
            </ghost-button>
          </Popconfirm>
        </Space>
      </template>
    </BasicTable>
    <TemplateModal @reload="tableApi.query()" />
  </Page>
</template>
