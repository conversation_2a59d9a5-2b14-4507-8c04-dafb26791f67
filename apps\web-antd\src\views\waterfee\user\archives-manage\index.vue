<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';

import type { VxeGridProps } from '#/adapter/vxe-table';

import { Page, useVbenDrawer, useVbenModal } from '@vben/common-ui';

import { Modal, Space } from 'ant-design-vue';

import { useVbenVxeGrid, vxeCheckboxChecked } from '#/adapter/vxe-table';
import {
  batchCancelSpecific,
  batchSetSpecific,
  insertMockData,
  listUser,
  UserExport,
} from '#/api/waterfee/user/archivesManage';
import { commonDownloadExcel } from '#/utils/file/download';

import { columns, querySchema } from './data';
import userActivateModal from './user-activate-modal.vue';
import userBasicInfoChangeDrawer from './user-basic-info-change-drawer.vue';
import userCancellationModal from './user-cancellation-modal.vue';
import userDeactivateModal from './user-deactivate-modal.vue';
import userDetailDrawer from './user-detail-drawer.vue';
import userImportModal from './user-import-modal.vue';
import userPriceChangeDrawer from './user-price-change-drawer.vue';
import userTransferDrawer from './user-transfer-drawer.vue';

/**
 * 导入
 */
const [UserImpotModal, userImportModalApi] = useVbenModal({
  connectedComponent: userImportModal,
});

function handleImport() {
  userImportModalApi.open();
}

const formOptions: VbenFormProps = {
  commonConfig: {
    labelWidth: 80,
    componentProps: {
      allowClear: true,
    },
  },
  schema: querySchema(),
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
};

const gridOptions: VxeGridProps = {
  checkboxConfig: {
    // 高亮
    highlight: true,
    // 翻页时保留选中状态
    reserve: true,
  },
  columns,
  height: 'auto',
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues = {}) => {
        return await listUser({
          pageNum: page.currentPage,
          pageSize: page.pageSize,
          auditStatus: 'finish',
          ...formValues,
        });
      },
    },
  },
  rowConfig: {
    keyField: 'userId',
  },
  id: 'waterfee-user-index',
};

const [BasicTable, tableApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

// 过户抽屉
const [UserTransferDrawer, transferDrawerApi] = useVbenDrawer({
  connectedComponent: userTransferDrawer,
});

// 用水价格变更抽屉
const [UserPriceChangeDrawer, priceChangeDrawerApi] = useVbenDrawer({
  connectedComponent: userPriceChangeDrawer,
});

// 基础信息变更抽屉
const [UserBasicInfoChangeDrawer, basicInfoChangeDrawerApi] = useVbenDrawer({
  connectedComponent: userBasicInfoChangeDrawer,
});

// 详情抽屉
const [UserDetailDrawer, detailDrawerApi] = useVbenDrawer({
  connectedComponent: userDetailDrawer,
});

// 销户弹窗
const [UserCancellationModal, cancellationModalApi] = useVbenModal({
  connectedComponent: userCancellationModal,
});

// 报停弹窗
const [UserDeactivateModal, deactivateModalApi] = useVbenModal({
  connectedComponent: userDeactivateModal,
});

// 启用弹窗
const [UserActivateModal, activateModalApi] = useVbenModal({
  connectedComponent: userActivateModal,
});

function handleDownloadExcel() {
  commonDownloadExcel(
    UserExport,
    '用水用户管理数据',
    tableApi.formApi.form.values,
  );
}

function handleTransfer(row: any) {
  transferDrawerApi.setData({ row });
  transferDrawerApi.open();
}

function handlePriceChange(row: any) {
  priceChangeDrawerApi.setData({ row });
  priceChangeDrawerApi.open();
}

function handleBasicInfoChange(row: any) {
  basicInfoChangeDrawerApi.setData({ row });
  basicInfoChangeDrawerApi.open();
}

function handleDetail(row: any) {
  detailDrawerApi.setData({ row });
  detailDrawerApi.open();
}

function handleCancellation(row: any) {
  cancellationModalApi.setData({ row });
  cancellationModalApi.open();
}

function handleDeactivate(row: any) {
  deactivateModalApi.setData({ row });
  deactivateModalApi.open();
}

function handleActivate(row: any) {
  activateModalApi.setData({ row });
  activateModalApi.open();
}

function handleBatchActivate() {
  const rows = tableApi.grid.getCheckboxRecords();
  if (rows.length === 0) return;

  Modal.confirm({
    title: '启用确认',
    content: `确定要启用选中的${rows.length}个用户吗？`,
    onOk: () => {
      handleActivate(rows);
    },
  });
}

function handleBatchDeactivate() {
  const rows = tableApi.grid.getCheckboxRecords();
  if (rows.length === 0) return;

  Modal.confirm({
    title: '报停确认',
    content: `确定要报停选中的${rows.length}个用户吗？`,
    onOk: () => {
      handleDeactivate(rows);
    },
  });
}

function handleBatchCancellation() {
  const rows = tableApi.grid.getCheckboxRecords();
  if (rows.length === 0) return;

  Modal.confirm({
    title: '销户确认',
    content: `确定要销户选中的${rows.length}个用户吗？`,
    onOk: () => {
      handleCancellation(rows);
    },
  });
}

function handleBatchSetSpecific() {
  const rows = tableApi.grid.getCheckboxRecords();
  if (rows.length === 0) return;

  Modal.confirm({
    title: '重点户设置',
    content: `确定要设置选中的${rows.length}个用户为重点户吗？`,
    onOk: () => {
      batchSetSpecific(rows.map((item: any) => item.userId));
    },
  });
}

function handleBatchCancelSpecific() {
  const rows = tableApi.grid.getCheckboxRecords();
  if (rows.length === 0) return;

  Modal.confirm({
    title: '重点户取消',
    content: `确定要设置选中的${rows.length}个用户为非重点户吗？`,
    onOk: () => {
      batchCancelSpecific(rows.map((item: any) => item.userId));
    },
  });
}

function handleMockData() {
  Modal.confirm({
    title: 'Mock数据确认',
    content: '确定要插入Mock数据吗？',
    onOk: async () => {
      try {
        await insertMockData();
        tableApi.query();
      } catch (error) {
        console.error('Mock数据插入失败:', error);
      }
    },
  });
}
</script>

<template>
  <Page :auto-content-height="true">
    <BasicTable table-title="用水用户管理列表">
      <template #toolbar-tools>
        <Space> 
          <a-button
            :disabled="!vxeCheckboxChecked(tableApi)"
            ghost
            type="primary"
            v-access:code="['waterfee:user:edit']"
            @click="handleBatchSetSpecific"
          >
            设为重点户
          </a-button>
          <a-button
            :disabled="!vxeCheckboxChecked(tableApi)"
            ghost
            type="primary"
            v-access:code="['waterfee:user:edit']"
            @click="handleBatchCancelSpecific"
          >
            设为非重点户
          </a-button>
          <a-button
            :disabled="!vxeCheckboxChecked(tableApi)"
            ghost
            type="primary"
            v-access:code="['waterfee:user:edit']"
            @click="handleBatchActivate"
          >
            启用
          </a-button>
          <a-button
            :disabled="!vxeCheckboxChecked(tableApi)"
            danger
            ghost
            type="primary"
            v-access:code="['waterfee:user:edit']"
            @click="handleBatchDeactivate"
          >
            报停
          </a-button>
          <a-button
            :disabled="!vxeCheckboxChecked(tableApi)"
            danger
            type="primary"
            v-access:code="['waterfee:user:edit']"
            @click="handleBatchCancellation"
          >
            销户
          </a-button>
          <a-button
            v-access:code="['waterfee:user:import']"
            @click="handleImport"
          >
            {{ $t('pages.common.import') }}
          </a-button>
          <a-button
            v-access:code="['waterfee:user:export']"
            @click="handleDownloadExcel"
          >
            {{ $t('pages.common.export') }}
          </a-button>
          <a-button
            type="primary"
            ghost
            @click="handleMockData"
          >
            Mock数据
          </a-button>
        </Space>
      </template>
      <template #action="{ row }">
        <Space>
          <ghost-button
            v-access:code="['waterfee:user:edit']"
            @click="handleTransfer(row)"
          >
            过户
          </ghost-button>
          <ghost-button
            v-access:code="['waterfee:user:edit']"
            @click="handlePriceChange(row)"
          >
            用水价格变更
          </ghost-button>
          <ghost-button
            v-access:code="['waterfee:user:edit']"
            @click="handleBasicInfoChange(row)"
          >
            基础信息变更
          </ghost-button>
          <ghost-button
            v-access:code="['waterfee:user:detail']"
            @click="handleDetail(row)"
          >
            详情
          </ghost-button>
        </Space>
      </template>
    </BasicTable>
    <UserDrawer @reload="tableApi.query()" />
    <UserTransferDrawer @reload="tableApi.query()" />
    <UserPriceChangeDrawer @reload="tableApi.query()" />
    <UserBasicInfoChangeDrawer @reload="tableApi.query()" />
    <UserDetailDrawer />
    <UserCancellationModal @reload="tableApi.query()" />
    <UserDeactivateModal @reload="tableApi.query()" />
    <UserActivateModal @reload="tableApi.query()" />
    <UserImpotModal @reload="tableApi.query()" />
  </Page>
</template>
