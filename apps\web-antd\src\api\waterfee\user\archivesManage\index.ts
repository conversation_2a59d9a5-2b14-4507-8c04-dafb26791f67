import type { PageQuery } from '#/api/common';
import type {
  UserFlowStepsInfo,
  UserForm,
  UserQuery,
  UserVO,
  WaterfeeUserBatchCancellationBo,
  WaterfeeUserBatchDeactivateBo,
} from '#/api/waterfee/user/archivesManage/model.d';
import type { UserBasicInfoChangeRecordForm } from '#/api/waterfee/user/basicInfoChangeRecord/model.d';
import type { UserCancellationRecordForm } from '#/api/waterfee/user/cancellationRecord/model.d';
import type { UserDeactivateRecordForm } from '#/api/waterfee/user/deactivateRecord/model.d';
import type { UserPriceChangeRecordForm } from '#/api/waterfee/user/priceChangeRecord/model.d';
import type { UserTransferOwnershipRecordForm } from '#/api/waterfee/user/transferOwnershipRecord/model.d';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

enum Api {
  activate = '/waterfee/user/activate',
  audit = '/waterfee/user/audit',
  basicInfoChange = '/waterfee/user/basicInfoChange',
  batchActivate = '/waterfee/user/batchActivate',
  batchCancellation = '/waterfee/user/batchCancellation',
  batchCancelSpecific = '/waterfee/user/batchCancelSpecific',
  batchDeactivate = '/waterfee/user/batchDeactivate',
  batchSetSpecific = '/waterfee/user/batchSetSpecific',
  cancellation = '/waterfee/user/cancellation',
  deactivate = '/waterfee/user/deactivate',
  list = '/waterfee/user/list',
  mockData = '/waterfee/demo/insertUserAndMeter',
  priceChange = '/waterfee/user/priceChange',
  root = '/waterfee/user',
  transfer = '/waterfee/user/transfer',
  userImport = '/waterfee/user/importData',
  userImportTemplate = '/waterfee/user/importTemplate',
}

/**
 * 用水用户管理导出
 * @param data data
 * @returns void
 */
export function UserExport(data: Partial<UserForm>) {
  return commonExport(`${Api.root}/export`, data);
}

/**
 * Mock数据插入
 * @returns void
 */
export function insertMockData() {
  return requestClient.get<void>(Api.mockData);
}

/**
 * 从excel导入用户
 * @param data
 * @returns void
 */
export function userImportData(data: FormData) {
  return requestClient.post<{ code: number; msg: string }>(
    Api.userImport,
    data,
    {
      // headers: {
      //   'Content-Type': ContentTypeEnum.FORM_DATA,
      // },
      isTransformResponse: false,
    },
  );
}

/**
 * 下载用户导入模板
 * @returns blob
 */
export function downloadImportTemplate() {
  return requestClient.post<Blob>(
    Api.userImportTemplate,
    {},
    {
      isTransformResponse: false,
      responseType: 'blob',
    },
  );
}

/**
 * 查询用水用户管理列表
 * @param params 查询参数
 * @returns 用水用户管理列表
 */
export function listUser(params?: PageQuery & UserQuery) {
  return requestClient.get<{ rows: UserVO[]; total: number }>(Api.list, {
    params,
  });
}

/**
 * 查询用水用户管理详细
 * @param userId 用水用户管理ID
 * @returns 用水用户管理信息
 */
export function getUser(userId: string) {
  return requestClient.get<UserForm>(`${Api.root}/${userId}`);
}

export function getUserFlowDetail(userId: string) {
  return requestClient.get<UserFlowStepsInfo>(
    `${Api.root}/workFlowGetDetail/${userId}`,
  );
}

// 立户报装-新增
export function registerUser(data: UserForm) {
  return requestClient.postWithMsg<void>(Api.root, data);
}
// 立户报装-修改
export function modifyUser(data: UserForm) {
  return requestClient.putWithMsg<void>(Api.root, data);
}

/**
 * 新增用水用户管理
 * @param data 新增数据
 * @returns void
 */
export function addUser(data: UserForm) {
  return requestClient.postWithMsg<void>(`${Api.root}/basicInfo`, data);
}

/**
 * 修改用水用户管理
 * @param data 修改数据
 * @returns void
 */
export function updateUser(data: UserForm) {
  return requestClient.putWithMsg<void>(`${Api.root}/basicInfo`, data);
}

/**
 * 删除用水用户管理
 * @param userId 用水用户管理ID或ID数组
 * @returns void
 */
export function delUser(userId: Array<number | string> | number | string) {
  return requestClient.deleteWithMsg<void>(`${Api.root}/${userId}`);
}

/**
 * 审核用水用户
 * @param userId 用水用户管理ID
 * @param auditStatus 审核状态
 * @returns void
 */
export function auditUser(userId: number | string, auditStatus: string) {
  return requestClient.putWithMsg<void>(`${Api.audit}/${userId}`, {
    auditStatus,
  });
}

/**
 * 用水用户过户
 * @param data 过户信息
 * @returns void
 */
export function transferUser(data: UserTransferOwnershipRecordForm) {
  return requestClient.postWithMsg<void>(Api.transfer, data);
}

/**
 * 用水价格变更
 * @param data 价格变更信息
 * @returns void
 */
export function changePriceUser(data: UserPriceChangeRecordForm) {
  return requestClient.postWithMsg<void>(Api.priceChange, data);
}

/**
 * 启用用水用户
 * @param userId 用水用户管理ID
 * @returns void
 */
export function activateUser(userId: number | string) {
  return requestClient.putWithMsg<void>(`${Api.activate}/${userId}`);
}

/**
 * 报停用水用户
 * @param data 报停信息
 * @returns void
 */
export function deactivateUser(data: UserDeactivateRecordForm) {
  return requestClient.postWithMsg<void>(Api.deactivate, data);
}

/**
 * 用水用户销户
 * @param data 销户信息
 * @returns void
 */
export function cancellationUser(data: UserCancellationRecordForm) {
  return requestClient.postWithMsg<void>(Api.cancellation, data);
}

/**
 * 用水用户基础信息变更
 * @param data 基础信息变更数据
 * @returns void
 */
export function changeBasicInfoUser(data: UserBasicInfoChangeRecordForm) {
  return requestClient.postWithMsg<void>(Api.basicInfoChange, data);
}

/**
 * 批量启用用水用户
 * @param userIds 用户ID数组
 * @returns void
 */
export function batchActivateUser(userIds: Array<number | string>) {
  return requestClient.postWithMsg<void>(Api.batchActivate, userIds);
}

/**
 * 批量报停用水用户
 * @param data 批量报停信息
 * @returns void
 */
export function batchDeactivateUser(data: WaterfeeUserBatchDeactivateBo) {
  return requestClient.postWithMsg<void>(Api.batchDeactivate, data);
}

/**
 * 批量销户用水用户
 * @param data 批量销户信息
 * @returns void
 */
export function batchCancellationUser(data: WaterfeeUserBatchCancellationBo) {
  return requestClient.postWithMsg<void>(Api.batchCancellation, data);
}

/**
 * 批量设定为重点户
 * @param userIds 用户ID数组
 * @returns void
 */
export function batchSetSpecific(userIds: Array<number | string>) {
  return requestClient.postWithMsg<void>(Api.batchSetSpecific, userIds);
}

/**
 * 批量取消重点户
 * @param userIds 用户ID数组
 * @returns void
 */
export function batchCancelSpecific(userIds: Array<number | string>) {
  return requestClient.postWithMsg<void>(Api.batchCancelSpecific, userIds);
}
