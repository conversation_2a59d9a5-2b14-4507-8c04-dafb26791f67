<script setup lang="ts">
import { computed, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { cloneDeep } from '@vben/utils';

import { useVbenForm } from '#/adapter/form';
import {
  templateAdd,
  templateInfo,
  templateUpdate,
} from '#/api/waterfee/template';

import { modalSchema } from './data';

const emit = defineEmits<{ reload: [] }>();

const isUpdate = ref(false);
const title = computed(() => {
  return isUpdate.value ? $t('pages.common.edit') : $t('pages.common.add');
});

const [BasicForm, formApi] = useVbenForm({
  commonConfig: {
    // 默认占满两列
    formItemClass: 'col-span-2',
    // 默认label宽度 px
    labelWidth: 100,
    // 通用配置项 会影响到所有表单项
    componentProps: {
      class: 'w-full',
    },
  },
  schema: modalSchema(),
  showDefaultActions: false,
  wrapperClass: 'grid-cols-2',
});

const [BasicModal, modalApi] = useVbenModal({
  class: 'w-[800px]',
  fullscreenButton: true,
  closeOnClickModal: false,
  onCancel: handleCancel,
  onConfirm: handleConfirm,
  onOpenChange: async (isOpen) => {
    if (!isOpen) {
      return null;
    }
    modalApi.modalLoading(true);

    // 初始化下拉选项
    // await initSelectOptions();

    const { id } = modalApi.getData() as { id?: number | string };
    isUpdate.value = !!id;

    if (isUpdate.value && id) {
      const record = await templateInfo(id);
      await formApi.setValues(record);
    } else {
      // 新增时设置默认值
      await formApi.setValues({
        isEnabled: '1',
        templateType: '',
        templateCategory: '',
      });
    }

    modalApi.modalLoading(false);
  },
});

async function handleConfirm() {
  try {
    modalApi.modalLoading(true);
    const values = await formApi.validate();

    // 处理文件上传后的数据
    const data = cloneDeep(values);

    // 如果fileUrl是数组，取第一个元素
    if (Array.isArray(data.fileUrl) && data.fileUrl.length > 0) {
      data.fileUrl = data.fileUrl[0];
    }

    // 从文件URL中提取文件名
    if (data.fileUrl && !data.fileName) {
      const urlParts = data.fileUrl.split('/');
      data.fileName = urlParts[urlParts.length - 1];
    }

    // 确保必填字段不为空
    if (!data.fileUrl) {
      throw new Error('请上传模板文件');
    }

    await (isUpdate.value ? templateUpdate(data) : templateAdd(data));
    emit('reload');
    await handleCancel();
  } catch (error) {
    console.error(error);
  } finally {
    modalApi.modalLoading(false);
  }
}

async function handleCancel() {
  modalApi.close();
  await formApi.resetFields();
}
</script>

<template>
  <BasicModal :title="title">
    <BasicForm />
  </BasicModal>
</template>
